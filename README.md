# 小说改编系统

基于Flask + Celery + Redis构建的网络小说智能改编系统，支持用户上传TXT文件，后台异步进行逐章改编处理。

## 功能特性

- 🔐 **用户认证**: 简单的用户名/密码登录系统
- 📁 **文件上传**: 支持TXT文件上传和拖拽操作
- 🤖 **智能改编**: 基于AI的小说内容改编，保持原有故事脉络
- ⚡ **异步处理**: 使用Celery进行后台异步任务处理
- 📊 **实时监控**: 实时显示改编进度和状态
- 📥 **结果下载**: 改编完成后提供文件下载
- 🎨 **简洁界面**: 基于Bootstrap的响应式前端设计

## 技术栈

- **后端**: Flask + SQLAlchemy + Celery
- **前端**: Jinja2模板 + JavaScript + Bootstrap 5
- **数据库**: SQLite (开发) / PostgreSQL (生产)
- **任务队列**: Redis + Celery
- **AI接口**: OpenAI兼容API

## 快速开始

### 环境要求

- Python 3.8+
- Redis 服务器
- 足够的磁盘空间用于文件存储

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd story_teller
   ```

2. **运行安装脚本**
   ```bash
   ./start.sh
   ```

3. **启动开发环境**
   ```bash
   ./dev_start.sh
   ```

4. **访问应用**
   - 打开浏览器访问: http://localhost:8080
   - 默认用户名: `admin`
   - 默认密码: `admin123`

### 手动启动

如果自动脚本无法使用，可以手动启动：

1. **启动Redis**
   ```bash
   # macOS
   brew services start redis
   
   # Ubuntu
   sudo systemctl start redis
   ```

2. **创建虚拟环境并安装依赖**
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

3. **启动Celery工作进程**
   ```bash
   celery -A celery_worker.celery worker --loglevel=info
   ```

4. **启动Flask应用**
   ```bash
   python run.py
   ```

## 使用说明

### 1. 登录系统
使用默认账号或管理员创建的账号登录系统。

### 2. 上传文件
- 点击"上传新文件"按钮
- 拖拽或选择TXT格式的小说文件
- 填写改编参数：
  - 任务名称
  - 主角名称
  - 书名
  - 频道（男频/女频）
  - 人称（第一/第三人称）
  - 高级选项（章节范围、批次大小等）

### 3. 监控进度
- 在控制台查看所有任务状态
- 点击"查看详情"监控实时进度
- 查看处理日志和错误信息

### 4. 下载结果
任务完成后，点击"下载"按钮获取改编后的文件。

## 配置说明

### 环境变量

在生产环境中，建议设置以下环境变量：

```bash
export SECRET_KEY="your-secret-key"
export DATABASE_URL="postgresql://user:pass@localhost/dbname"
export CELERY_BROKER_URL="redis://localhost:6379/0"
export CELERY_RESULT_BACKEND="redis://localhost:6379/0"
export OPENAI_API_KEY="your-api-key"
export API_BASE_URL="your-api-base-url"
```

### 改编参数

- **频道**: 男频/女频，影响改编风格
- **人称**: 第一人称/第三人称，保持原文人称设定
- **章节范围**: 可指定处理特定章节范围
- **批次大小**: 每次处理的章节数量，影响处理速度和质量

## 管理命令

### 创建用户
```bash
python run.py create-user
```

### 创建管理员
```bash
python run.py create-admin
```

### 初始化数据库
```bash
python run.py init-db
```

## 文件结构

```
story_teller/
├── app/                    # 应用核心代码
│   ├── __init__.py        # 应用初始化
│   ├── models.py          # 数据库模型
│   ├── routes.py          # 路由处理
│   ├── tasks.py           # Celery任务
│   ├── forms.py           # 表单处理
│   └── utils.py           # 工具函数
├── templates/             # HTML模板
├── static/                # 静态文件
├── migrations/            # 数据库迁移
├── main.py               # 原始改编逻辑
├── config.py             # 配置文件
├── requirements.txt      # Python依赖
├── run.py               # 应用启动文件
├── celery_worker.py     # Celery工作进程
└── README.md            # 说明文档
```

## 故障排除

### 常见问题

1. **Redis连接失败**
   - 确保Redis服务正在运行
   - 检查Redis配置和端口

2. **Celery任务不执行**
   - 确保Celery工作进程正在运行
   - 检查Celery日志文件

3. **文件上传失败**
   - 检查文件大小限制（默认16MB）
   - 确保上传目录有写权限

4. **改编任务失败**
   - 检查API密钥配置
   - 查看任务详情页面的错误信息

### 日志文件

- Flask应用日志: 控制台输出
- Celery工作进程日志: `logs/celery.log`
- 任务处理日志: 数据库中的错误信息

## 停止服务

```bash
./stop.sh
```

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目采用MIT许可证。
