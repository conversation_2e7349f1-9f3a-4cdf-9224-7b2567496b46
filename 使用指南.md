# 小说改编系统使用指南

## 🚀 快速开始

### 1. 访问系统
- 打开浏览器访问：http://localhost:8080
- 使用默认账号登录：
  - 用户名：`admin`
  - 密码：`admin123`

### 2. 选择改编方式

系统提供两种简洁的改编方式：

#### 🔥 快速改编（推荐）
- **适用场景**：短篇内容、单章节、片段改编
- **操作步骤**：
  1. 点击"快速改编"按钮
  2. 直接粘贴小说内容（50-10000字）
  3. 填写改编参数（主角、书名、频道、人称）
  4. 点击"立即改编"

#### 📁 上传文件
- **适用场景**：较长的TXT文件
- **操作步骤**：
  1. 点击"上传文件"按钮
  2. 拖拽或选择TXT文件
  3. 填写改编参数
  4. 点击"开始改编"

## 📋 改编参数说明

### 必填参数
- **主角名称**：小说中的主要角色名字
- **书名**：小说的标题
- **频道**：
  - 男频：面向男性读者的小说风格
  - 女频：面向女性读者的小说风格
- **人称**：
  - 第一人称：以"我"的视角叙述
  - 第三人称：以"他/她"的视角叙述

## 🔄 改编流程

1. **提交内容** → 系统接收您的文本
2. **智能分析** → 自动识别章节结构和内容特点
3. **AI改编** → 基于参数进行智能改编
4. **实时监控** → 查看改编进度和状态
5. **下载结果** → 获取改编后的文件

## 📊 任务管理

### 查看任务状态
- **等待中**：任务已创建，等待处理
- **处理中**：正在进行改编，可查看实时进度
- **已完成**：改编完成，可下载结果
- **失败**：处理出错，查看错误信息

### 任务操作
- **查看详情**：监控实时进度和日志
- **下载结果**：获取改编后的文件
- **删除任务**：清理不需要的任务

## 💡 使用技巧

### 内容准备
- **最佳长度**：单次改编建议500-5000字
- **格式要求**：纯文本，避免特殊符号
- **章节标识**：使用"第X章"等标准格式

### 参数选择
- **主角名称**：确保名称在文本中出现
- **频道选择**：影响改编风格和语言特色
- **人称统一**：与原文保持一致

### 质量优化
- **分段处理**：长文本建议分章节处理
- **参数调整**：根据效果调整频道和人称
- **多次尝试**：可以用不同参数重新改编

## ⚠️ 注意事项

### 文件要求
- 仅支持TXT格式文件
- 文件大小限制：16MB
- 编码格式：UTF-8

### 内容限制
- 快速改编：50-10000字
- 避免包含敏感内容
- 确保内容完整性

### 系统限制
- 当前为演示版本，使用简化处理逻辑
- 实际改编需要配置API密钥
- 建议在开发环境中测试

## 🛠️ 故障排除

### 常见问题
1. **上传失败**
   - 检查文件格式是否为TXT
   - 确认文件大小不超过限制
   - 尝试重新选择文件

2. **改编失败**
   - 检查网络连接
   - 确认内容格式正确
   - 查看错误信息详情

3. **进度卡住**
   - 刷新页面查看最新状态
   - 检查任务详情页面
   - 联系技术支持

### 获取帮助
- 查看任务详情页面的错误信息
- 检查浏览器控制台日志
- 联系系统管理员

## 🎯 最佳实践

1. **内容准备**：确保文本格式规范，章节清晰
2. **参数设置**：根据内容特点选择合适的频道和人称
3. **分批处理**：长篇内容建议分章节逐个改编
4. **结果检查**：下载后仔细检查改编质量
5. **备份原文**：保留原始文件作为备份

---

**提示**：这是一个演示系统，主要用于展示小说改编的工作流程。在生产环境中使用时，请确保配置正确的API密钥和相关服务。
