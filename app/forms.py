from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, PasswordField, SelectField, SubmitField, TextAreaField
from wtforms.validators import DataRequired, Length

class LoginForm(FlaskForm):
    username = StringField('用户名', validators=[DataRequired(), Length(min=3, max=20)])
    password = PasswordField('密码', validators=[DataRequired(), Length(min=6, max=20)])
    submit = SubmitField('登录')

class UploadForm(FlaskForm):
    file = FileField('选择TXT文件', validators=[
        FileRequired(),
        FileAllowed(['txt'], '只支持TXT格式文件！')
    ])
    character = StringField('主角名称', validators=[DataRequired(), Length(min=1, max=50)])
    book_name = StringField('书名', validators=[DataRequired(), Length(min=1, max=100)])
    channel = SelectField('频道', choices=[
        ('男频', '男频'),
        ('女频', '女频')
    ], validators=[DataRequired()])
    person = SelectField('人称', choices=[
        ('一', '第一人称'),
        ('三', '第三人称')
    ], validators=[DataRequired()])
    submit = SubmitField('开始改编')

class QuickAdaptForm(FlaskForm):
    """快速改编表单 - 直接粘贴文本"""
    content = TextAreaField('小说内容', validators=[DataRequired(), Length(min=50, max=10000)],
                           render_kw={"rows": 10, "placeholder": "请粘贴需要改编的小说内容..."})
    character = StringField('主角名称', validators=[DataRequired(), Length(min=1, max=50)])
    book_name = StringField('书名', validators=[DataRequired(), Length(min=1, max=100)])
    channel = SelectField('频道', choices=[
        ('男频', '男频'),
        ('女频', '女频')
    ], validators=[DataRequired()])
    person = SelectField('人称', choices=[
        ('一', '第一人称'),
        ('三', '第三人称')
    ], validators=[DataRequired()])
    submit = SubmitField('立即改编')
