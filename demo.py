#!/usr/bin/env python3
"""
小说改编系统演示脚本
"""

import os
import time
import requests
from pathlib import Path

def demo_upload_and_process():
    """演示上传和处理流程"""
    
    print("=== 小说改编系统演示 ===")
    print()
    
    # 检查应用是否运行
    try:
        response = requests.get('http://localhost:8080')
        print("✓ 应用正在运行")
    except requests.exceptions.ConnectionError:
        print("✗ 应用未运行，请先启动应用:")
        print("  python run.py")
        return
    
    print()
    print("演示步骤:")
    print("1. 打开浏览器访问: http://localhost:8080")
    print("2. 使用默认账号登录:")
    print("   用户名: admin")
    print("   密码: admin123")
    print()
    print("3. 点击'上传新文件'按钮")
    print("4. 上传测试文件: test_novel.txt")
    print("5. 填写改编参数:")
    print("   - 任务名称: 测试改编")
    print("   - 主角名称: 李明")
    print("   - 书名: 穿越修仙传")
    print("   - 频道: 男频")
    print("   - 人称: 第三人称")
    print()
    print("6. 点击'开始改编'")
    print("7. 在任务详情页面查看实时进度")
    print("8. 改编完成后下载结果文件")
    print()
    
    # 检查测试文件是否存在
    test_file = Path("test_novel.txt")
    if test_file.exists():
        print(f"✓ 测试文件已准备: {test_file}")
        print(f"  文件大小: {test_file.stat().st_size} 字节")
    else:
        print("✗ 测试文件不存在，正在创建...")
        create_test_file()
    
    print()
    print("注意事项:")
    print("- 这是演示版本，使用简化的改编逻辑")
    print("- 实际改编需要配置正确的API密钥")
    print("- 建议在开发环境中测试")

def create_test_file():
    """创建测试文件"""
    content = """第一章 穿越异世

李明睁开眼睛，发现自己躺在一个陌生的房间里。

"这是哪里？"他疑惑地坐起身来，环顾四周。

房间很简陋，只有一张木床、一张桌子和一把椅子。墙上挂着一面铜镜，镜子里的人让他大吃一惊。

"这不是我的脸！"

镜子里的人看起来只有十六七岁，面容清秀，但明显不是他原来的样子。

就在这时，一阵记忆涌入他的脑海。原来这个身体的主人也叫李明，是个孤儿，在这个叫做天元大陆的世界里艰难求生。

"穿越了？"李明不敢置信地摸着自己的脸。

突然，门外传来脚步声。

"李明，你醒了吗？"一个温和的声音响起。

第二章 神秘老者

门被推开，走进来一个白发苍苍的老者。

"张爷爷？"李明脑海中的记忆告诉他，这是照顾原身的恩人。

"孩子，你昏迷了三天，可把我吓坏了。"张老头关切地说道。

李明努力整理着脑海中的记忆，这个世界是一个修仙世界，人们可以通过修炼获得强大的力量。

"张爷爷，我感觉好多了。"李明试探性地说道。

张老头仔细打量着他，眼中闪过一丝异色。

"孩子，你的气质好像变了。"

李明心中一紧，难道被发现了？

"可能是大病一场的缘故吧。"他勉强笑道。

张老头点点头，从怀中取出一本古朴的书籍。

"这是我年轻时得到的修炼功法，本想带进棺材，但看你的资质不错，就传给你吧。"

李明接过书籍，封面上写着《基础炼气诀》几个大字。"""

    with open("test_novel.txt", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✓ 测试文件创建完成")

if __name__ == "__main__":
    demo_upload_and_process()
