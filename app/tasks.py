import os
import logging
from datetime import datetime
from pathlib import Path
from celery import current_task
from flask import current_app
from app import celery, db
from app.models import AdaptationTask
from app.utils import split_text_into_chapters, create_chapter_files

# 暂时创建一个简化的TextRewriter类用于演示
import re

class BaseTextRewriter:
    def __init__(self, api_keys, character, book_name, channel, person, key_func):
        self.api_keys = api_keys
        self.character = character
        self.book_name = book_name
        self.channel = channel
        self.person = person
        self.key_func = key_func

    def process_directory_with_progress(self, input_dir, output_dir=None, num_attempts=2,
                                      start_chapter=None, end_chapter=None,
                                      chapters_per_batch=2, progress_callback=None):
        """简化版本的处理方法，用于演示"""
        if progress_callback:
            progress_callback(0, 100, "开始处理...")

        # 模拟处理过程
        import time
        for i in range(1, 101, 10):
            time.sleep(0.5)  # 模拟处理时间
            if progress_callback:
                progress_callback(i, 100, f"处理进度 {i}%...")

        # 创建一个示例输出文件
        output_path = Path(output_dir) if output_dir else Path(input_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        sample_output = output_path / "sample_output.txt"
        with open(sample_output, 'w', encoding='utf-8') as f:
            f.write(f"这是一个示例改编结果\n主角：{self.character}\n书名：{self.book_name}\n频道：{self.channel}")

        if progress_callback:
            progress_callback(100, 100, "处理完成！")

class ProgressAwareTextRewriter(BaseTextRewriter):
    """支持进度回调的TextRewriter"""

    def __init__(self, *args, **kwargs):
        self.progress_callback = kwargs.pop('progress_callback', None)
        super().__init__(*args, **kwargs)

    def process_directory_with_progress(self, input_dir, output_dir=None, num_attempts=2,
                                      start_chapter=None, end_chapter=None,
                                      chapters_per_batch=2, progress_callback=None):
        """带进度回调的目录处理方法"""
        if progress_callback:
            self.progress_callback = progress_callback

        # 复制原有逻辑，但添加进度回调
        input_path = Path(input_dir)
        output_path = Path(output_dir) if output_dir else input_path
        output_path.mkdir(parents=True, exist_ok=True)

        # 获取所有.txt文件但排除包含rewrite的文件
        input_files = sorted(
            [f for f in input_path.glob('*.txt') if 'rewrite' not in f.stem],
            key=lambda x: int(re.search(r'\d+', x.stem).group())
        )

        # 应用章节过滤
        if start_chapter is not None or end_chapter is not None:
            filtered_files = []
            for f in input_files:
                chapter_num = int(re.search(r'\d+', f.stem).group())
                if start_chapter is not None and chapter_num < start_chapter:
                    continue
                if end_chapter is not None and chapter_num > end_chapter:
                    continue
                filtered_files.append(f)
            input_files = filtered_files

        if not input_files:
            if self.progress_callback:
                self.progress_callback(0, 0, f"没有找到要处理的文件")
            return

        total_batches = (len(input_files) + chapters_per_batch - 1) // chapters_per_batch
        processed_batches = 0

        # 按批次处理文件
        for i in range(0, len(input_files), chapters_per_batch):
            batch_files = input_files[i:i+chapters_per_batch]

            # 获取批次信息
            first_chapter_num = int(re.search(r'\d+', batch_files[0].stem).group())
            last_chapter_num = int(re.search(r'\d+', batch_files[-1].stem).group())

            if self.progress_callback:
                self.progress_callback(
                    processed_batches,
                    total_batches,
                    f"处理第{first_chapter_num}-{last_chapter_num}章..."
                )

            # 检查是否已经处理过这个批次
            batch_output_name = f"chapter_{first_chapter_num}_{last_chapter_num}_rewrite.txt"
            batch_output_path = output_path / batch_output_name

            if batch_output_path.exists():
                logger.info(f"Skipping already processed batch: {batch_output_name}")
                processed_batches += 1
                continue

            # 处理批次（使用原有逻辑）
            try:
                self._process_batch(batch_files, output_path, first_chapter_num, last_chapter_num, num_attempts)
                processed_batches += 1

                if self.progress_callback:
                    self.progress_callback(
                        processed_batches,
                        total_batches,
                        f"完成第{first_chapter_num}-{last_chapter_num}章"
                    )
            except Exception as e:
                logger.error(f"处理批次失败: {str(e)}")
                if self.progress_callback:
                    self.progress_callback(
                        processed_batches,
                        total_batches,
                        f"处理第{first_chapter_num}-{last_chapter_num}章失败: {str(e)}"
                    )
                raise

    def _process_batch(self, batch_files, output_path, first_chapter_num, last_chapter_num, num_attempts):
        """处理单个批次"""
        # 获取pre_text
        pre_text = '无前文'
        if first_chapter_num > 1:
            # 查找上一个批次的输出文件
            previous_batch_files = sorted(
                [f for f in output_path.glob('*_rewrite.txt')],
                key=lambda x: int(re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name).group(1)) if re.search(r'chapter_(\d+)_\d+_rewrite\.txt', x.name) else 0
            )

            if previous_batch_files:
                # 取最后一个处理过的批次文件的后500字符作为pre_text
                last_batch_file = previous_batch_files[-1]
                try:
                    pre_text = last_batch_file.read_text(encoding='utf-8')[-500:]
                    logger.info(f"Retrieved pre_text from batch: {last_batch_file.name}")
                except Exception as e:
                    logger.error(f"Error reading batch file {last_batch_file.name}: {str(e)}")
                    pre_text = '无前文'

        # 合并当前批次的所有章节内容
        combined_text = ""
        chapter_info = []

        for file_path in batch_files:
            try:
                chapter_text = file_path.read_text(encoding='utf-8')
                chapter_num = int(re.search(r'\d+', file_path.stem).group())
                combined_text += f"\n\n=== 第{chapter_num}章 ===\n\n{chapter_text}"
                chapter_info.append(f"第{chapter_num}章")
                logger.info(f"Added chapter {chapter_num} to batch (length: {len(chapter_text)} characters)")
            except Exception as e:
                logger.error(f"Error reading file {file_path.name}: {str(e)}")
                continue

        if not combined_text:
            raise Exception(f"No valid content found in batch starting with chapter {first_chapter_num}")

        logger.info(f"Processing batch: {' + '.join(chapter_info)} (total length: {len(combined_text)} characters)")

        # 处理合并后的文本
        best_result = self._process_combined_text(combined_text, pre_text, num_attempts, f"batch_{first_chapter_num}_{last_chapter_num}")

        if best_result:
            # 保存结果
            batch_output_name = f"chapter_{first_chapter_num}_{last_chapter_num}_rewrite.txt"
            batch_output_path = output_path / batch_output_name
            batch_output_path.write_text(best_result, encoding='utf-8')
            logger.info(f"Successfully processed and saved batch: {batch_output_name} (length: {len(best_result)} characters)")
        else:
            raise Exception(f"Failed to process batch starting with chapter {first_chapter_num}")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@celery.task(bind=True)
def process_adaptation_task(self, task_id):
    """处理小说改编任务"""
    try:
        # 获取任务信息
        task = AdaptationTask.query.get(task_id)
        if not task:
            raise Exception(f"Task {task_id} not found")
        
        # 更新任务状态
        task.status = 'processing'
        task.started_at = datetime.utcnow()
        task.celery_task_id = self.request.id
        db.session.commit()
        
        # 更新进度
        self.update_state(state='PROGRESS', meta={'current': 0, 'total': 100, 'status': '开始处理...'})
        
        # 读取原始文件
        with open(task.file_path, 'r', encoding='utf-8') as f:
            text_content = f.read()
        
        # 分割章节
        self.update_state(state='PROGRESS', meta={'current': 10, 'total': 100, 'status': '分析章节结构...'})
        chapters = split_text_into_chapters(text_content)
        
        if not chapters:
            raise Exception("无法识别章节结构")
        
        # 更新总章节数
        task.total_chapters = len(chapters)
        db.session.commit()
        
        # 创建章节文件
        self.update_state(state='PROGRESS', meta={'current': 20, 'total': 100, 'status': '创建章节文件...'})
        chapters_dir, chapter_files = create_chapter_files(chapters, task.file_path)
        
        # 初始化TextRewriter
        self.update_state(state='PROGRESS', meta={'current': 30, 'total': 100, 'status': '初始化改编引擎...'})

        api_keys = current_app.config['API_KEYS']
        rewriter = ProgressAwareTextRewriter(
            api_keys=api_keys,
            character=task.character,
            book_name=task.book_name,
            channel=task.channel,
            person=task.person,
            key_func=lambda s: int(re.search(r'\d+', s.stem).group()) if re.search(r'\d+', s.stem) else None
        )
        
        # 设置输出目录
        output_dir = Path(task.file_path).parent / f"{Path(task.file_path).stem}_adapted"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 开始改编处理
        self.update_state(state='PROGRESS', meta={'current': 40, 'total': 100, 'status': '开始改编处理...'})
        
        # 使用现有的process_directory方法，但需要适配进度更新
        try:
            # 这里需要修改TextRewriter类以支持进度回调
            rewriter.process_directory_with_progress(
                input_dir=chapters_dir,
                output_dir=str(output_dir),
                num_attempts=1,
                start_chapter=getattr(task, 'start_chapter', None),
                end_chapter=getattr(task, 'end_chapter', None),
                chapters_per_batch=getattr(task, 'chapters_per_batch', 3),
                progress_callback=lambda current, total, status: update_task_progress(
                    self, task, current, total, status, 40, 90
                )
            )
        except Exception as e:
            logger.error(f"改编处理失败: {str(e)}")
            raise
        
        # 合并输出文件
        self.update_state(state='PROGRESS', meta={'current': 95, 'total': 100, 'status': '合并输出文件...'})
        final_output_path = merge_adapted_files(output_dir, task)
        
        # 更新任务完成状态
        task.status = 'completed'
        task.completed_at = datetime.utcnow()
        task.output_path = final_output_path
        task.progress = 100
        db.session.commit()
        
        self.update_state(state='SUCCESS', meta={'current': 100, 'total': 100, 'status': '改编完成！'})
        
        return {
            'status': 'completed',
            'output_path': final_output_path,
            'total_chapters': task.total_chapters,
            'processed_chapters': task.processed_chapters
        }
        
    except Exception as e:
        logger.error(f"任务处理失败: {str(e)}")
        
        # 更新任务失败状态
        if 'task' in locals():
            task.status = 'failed'
            task.error_message = str(e)
            task.completed_at = datetime.utcnow()
            db.session.commit()
        
        self.update_state(state='FAILURE', meta={'error': str(e)})
        raise

def update_task_progress(celery_task, db_task, current, total, status, min_progress, max_progress):
    """更新任务进度"""
    # 计算实际进度（在min_progress和max_progress之间）
    if total > 0:
        progress_ratio = current / total
        actual_progress = min_progress + (max_progress - min_progress) * progress_ratio
    else:
        actual_progress = min_progress
    
    # 更新数据库
    db_task.progress = int(actual_progress)
    db_task.processed_chapters = current
    db.session.commit()
    
    # 更新Celery状态
    celery_task.update_state(
        state='PROGRESS',
        meta={
            'current': int(actual_progress),
            'total': 100,
            'status': status,
            'chapters_current': current,
            'chapters_total': total
        }
    )

def merge_adapted_files(output_dir, task):
    """合并改编后的文件"""
    output_files = sorted(Path(output_dir).glob('*_rewrite.txt'))
    
    if not output_files:
        raise Exception("没有找到改编后的文件")
    
    # 创建最终输出文件
    final_filename = f"{task.task_name}_adapted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    final_path = Path(output_dir).parent / final_filename
    
    with open(final_path, 'w', encoding='utf-8') as outfile:
        outfile.write(f"《{task.book_name}》改编版\n")
        outfile.write(f"主角：{task.character}\n")
        outfile.write(f"改编时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        outfile.write("=" * 50 + "\n\n")
        
        for i, file_path in enumerate(output_files, 1):
            with open(file_path, 'r', encoding='utf-8') as infile:
                content = infile.read().strip()
                outfile.write(f"\n\n=== 第{i}部分 ===\n\n")
                outfile.write(content)
                outfile.write("\n\n")
    
    return str(final_path)
